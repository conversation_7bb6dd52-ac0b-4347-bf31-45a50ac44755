import { env } from "$env/dynamic/private";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { proxy } from "$lib/utils/proxy"
import type { GeminiRequest } from "$lib/models/gemini.model";

export const POST: RequestHandler = async ({ request, url }) => {
    const requestBody: GeminiRequest = await request.json();

    // 在开发环境中使用代理请求
    const proxyResp = await proxy.json.post(url.pathname + url.search, {
        body: JSON.stringify(requestBody)
    });
    if (proxyResp) return proxyResp;

    console.log('request', url.pathname + url.search);

    if (!env.GEMINI_API_KEY) {
        return new Response(JSON.stringify({
            success: false,
            error: "GEMINI_API_KEY not set"
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    try {
        const resp = await fetch(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`,
            {
                method: "POST",
                headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                    "x-goog-api-key": env.GEMINI_API_KEY,
                },
                body: JSON.stringify(requestBody),
            }
        );
        const headers = new Headers(resp.headers);
        headers.delete('content-encoding');
        headers.delete('content-length');
        return new Response(resp.body, {
            status: resp.status,
            headers: headers,
        });
    } catch (err: any) {
        return new Response(JSON.stringify({
            success: false,
            error: `Generate error: ${err}`
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}