<script lang="ts">
    import type { KeyboardShortcut } from "$lib/actions/window";
    import AutoHeightTextarea from "$lib/components/ui/AutoHeightTextarea.svelte";

    import {
        createGeminiRequest,
        joinGeminiResponse,
        type GeminiResponse,
    } from "$lib/models/gemini.model";
    import { Key } from "@lucide/svelte";

    let timer: any;
    let text = $state("");
    let suggestion = $state("");

    // 添加快捷键操作
    const shortcuts: KeyboardShortcut[] = [
        {
            key: "Tab",
            handle: () => {
                if (suggestion) {
                    text = suggestion;
                    suggestion = "";
                    handleInput();
                    return true;
                }
                return false;
            },
        },
    ];

    async function autoAiGenerate() {
        if (!text || text.length < 10) {
            return;
        }

        const response = await fetch("/api/ai/generate", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(
                createGeminiRequest([text], {
                    prompt: "请续写小说, 最多 100 字.",
                }),
            ),
        });
        const json: GeminiResponse = await response.json();

        // console.log(json);

        suggestion = text + joinGeminiResponse(json).replace(text, "");
    }

    function handleInput() {
        clearTimeout(timer);
        timer = setTimeout(() => {
            autoAiGenerate();
        }, 3000);
    }
</script>

<div class="max-w-xl mx-auto text-center">
    <h1>蘑菇🍄 AI 小说创作平台</h1>
    <AutoHeightTextarea
        class="p-2 border"
        placeholder="小说正文"
        bind:value={text}
        {suggestion}
        on:input={handleInput}
        {shortcuts}
    ></AutoHeightTextarea>
</div>
