<script lang="ts">
  import { addToast } from "$lib/components/toast/toastStore";
  import { postGeminiJson } from "$lib/services/gemini.service";

  export let disabled = false;
  export let text = "";
  export let input: string;
  export let prompt: string = "";
  export let datatype: string = "";
  export let count: number = 3;

  export let extraPrompt: () => string = () => "";

  export let value: any;

  let loading = false;

  let list: any[] = [];
  let index = 0;

  function handleAIAutoGenerate() {
    loading = true;
    let aiPrompt = "";
    if (datatype && datatype.trim() !== "") {
      aiPrompt = `${datatype}\n------------------------\n回复内容请务必使用以上类型结构返回格式化的 json 数据，数据结构必须完整，不可缺少字段或属性（强制），字段或属性不可为空（强制）。\n----------------------\n`;
    }
    if (0 < count) {
      aiPrompt = `${aiPrompt}最多返回 ${count} 条数据，以数组结构形式返回（强制）。\n------------------------\n`;
    }
    if (prompt && prompt.trim() !== "") {
      aiPrompt = `${aiPrompt}${prompt}`;
    }
    let extra = extraPrompt();
    if (extra && extra.trim() !== "") {
      aiPrompt = `${aiPrompt}\n-----------------------\n${extra}`;
    }
    console.log(aiPrompt);

    navigator.clipboard.writeText(aiPrompt + "\n" + input);
    postGeminiJson(input, { prompt: aiPrompt, isText: datatype.trim() === "" })
      .then((obj: any) => {
        console.log(obj);

        if (Array.isArray(obj)) {
          list = obj;
        } else {
          list = [obj];
        }

        value = list[index];
      })
      .catch((e) => {
        addToast({
          message: "一键生成失败\n" + e.message,
          type: "error",
          duration: 5000,
        });
      })
      .finally(() => {
        loading = false;
      });
  }

  function handlePrev() {
    index = index <= 0 ? 0 : index - 1;
    value = list[index];
  }

  function handleNext() {
    index = index >= list.length - 1 ? 0 : index + 1;
    value = list[index];
  }
</script>

<div class="flex flex-row items-center">
  {#if 1 < count}
    <div class="text-gray-500 hover:bg-gray-100 hover:text-gray-700">
      <button class="px-1 hover:bg-gray-200" on:click={handlePrev}>◀️</button>
      <span>{list.length == 0 ? 0 : index + 1}/{list.length}</span>
      <button class="px-1 hover:bg-gray-200" on:click={handleNext}>▶️</button>
    </div>
  {/if}
  <button
    on:click={handleAIAutoGenerate}
    disabled={disabled || loading}
    class="flex items-center auto-generate disabled:opacity-50 transition-colors p-2 hover:bg-red-50 disabled:hover:bg-transparent disabled:cursor-not-allowed rounded-lg"
    title={text}
    aria-label={text}
  >
    <span role="img" class="semi-icon semi-icon-default h-fit"
      ><svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 20 20"
        ><path
          fill="url(#ai-generate_svg__a)"
          fill-rule="evenodd"
          d="M12.083 5q-.162 0-.184-.179a8 8 0 0 0-.14-.85q-.07-.31-.233-.461-.157-.157-.476-.238-.314-.082-.866-.174Q10 3.071 10 2.914q0-.151.162-.179.558-.108.877-.19.319-.086.482-.238.162-.15.238-.45.075-.303.14-.845.022-.18.184-.179.163 0 .184.174.07.548.141.861.075.315.233.477.162.157.481.233.32.075.882.157a.16.16 0 0 1 .114.054.17.17 0 0 1 .049.125q0 .151-.163.184-.563.108-.882.195-.314.081-.476.239-.157.15-.233.455-.075.303-.146.845a.2.2 0 0 1-.06.12.17.17 0 0 1-.124.048m-8.59 1.416q.03.25.257.25.106 0 .174-.068a.3.3 0 0 0 .083-.166q.099-.759.205-1.184t.326-.637q.227-.22.666-.334.447-.12 1.235-.273.228-.045.228-.258a.24.24 0 0 0-.069-.174.23.23 0 0 0-.159-.076 15 15 0 0 1-1.235-.22q-.446-.106-.674-.326-.22-.228-.326-.668a14 14 0 0 1-.197-1.206Q3.977.833 3.75.833t-.258.25q-.09.758-.197 1.184-.105.417-.333.63-.227.211-.674.333-.447.114-1.227.266-.228.038-.228.25 0 .22.258.258.773.129 1.212.243.447.114.667.334.227.212.325.644.107.434.197 1.191m-.76 7.572q.02.18.184.179.075 0 .124-.05a.2.2 0 0 0 .06-.118 8 8 0 0 1 .146-.846q.075-.303.232-.455.163-.156.477-.238.318-.087.882-.195Q5 12.232 5 12.08a.17.17 0 0 0-.049-.125.16.16 0 0 0-.113-.054q-.564-.082-.882-.157-.32-.075-.482-.233-.157-.163-.233-.477a10 10 0 0 1-.14-.862Q3.079 10 2.917 10q-.163 0-.184.179a7 7 0 0 1-.141.845q-.075.299-.238.45-.162.15-.482.238-.32.082-.876.19-.163.027-.163.179 0 .156.184.184.552.092.866.173.32.081.476.**************.************.85m5.76-6.674L7.314 8.493l2.956 2.955 1.178-1.178zm8.848 8.848c.65-.65.65-1.706 0-2.357l-4.714-4.714L9.67 6.136a1.667 1.667 0 0 0-2.357 0L6.136 7.314c-.651.65-.651 1.706 0 2.357l7.67 7.67c.65.65 1.705.65 2.356 0z"
          clip-rule="evenodd"
        ></path><defs
          ><linearGradient
            id="ai-generate_svg__a"
            x1="3.889"
            x2="17.778"
            y1="1.111"
            y2="15.556"
            gradientUnits="userSpaceOnUse"
            ><stop stop-color="#FF7D0D"></stop><stop
              offset="1"
              stop-color="#FF38A4"
            ></stop>
          </linearGradient>
        </defs>
      </svg>
    </span>&nbsp;
    <span>{text}</span>
    <span
      class:hidden={!loading}
      class="ml-1 animate-spin h-4 w-4 border-2 border-purple-500 border-t-transparent rounded-full"
    ></span>
  </button>
</div>

<style>
  .auto-generate span {
    background: linear-gradient(90deg, #ff37a3 4.46%, #9c4eff);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
</style>
