export interface GeminiRequest {
  contents: {
    role: string;
    parts: { text: string }[];
  }[];
  generationConfig: {
    temperature: number;
    maxOutputTokens: number;
    topP: number;
  };
  safetySettings: {
    category: string;
    threshold: string;
  }[];
}

export interface GeminiResponse {
  candidates?: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}

// createGeminiRequest 返回 GeminiRequest
export function createGeminiRequest(texts: string[], generationConfig?: {
  prompt?: string,
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
}): GeminiRequest {
  const inputs = texts.map((item) => ({ text: item }));
  const parts = generationConfig?.prompt ? [{ text: generationConfig.prompt }, ...inputs] : inputs;
  return {
    contents: [
      {
        role: "user",
        parts,
      },
    ],
    generationConfig: {
      temperature: generationConfig?.temperature ?? 0.6,
      maxOutputTokens: generationConfig?.maxOutputTokens ?? 6000,
      topP: generationConfig?.topP ?? 1,
    },
    safetySettings: [
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
    ],
  };
}

// 拼接 GeminiResponse 对象为字符串
export function joinGeminiResponse(res: GeminiResponse): string {
  return res.candidates?.[0]?.content?.parts?.map((item) => item.text).join("") ?? "";
}