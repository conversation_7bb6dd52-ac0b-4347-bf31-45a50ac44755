import { geminiConfig } from "$lib/models/app-state.model";
import { createGeminiRequest, type GeminiRequest } from "$lib/models/gemini.model";
import { get } from 'svelte/store';

export async function postGemini(inputText: string, options?: { prompt?: string, maxLenght?: number }): Promise<any[]> {
  const config = get(geminiConfig);
  const text = inputText + (options?.maxLenght ? `\n请最多返回 ${options.maxLenght} 个字` : "");

  const requestBody = createGeminiRequest([text], {
    prompt: options?.prompt,
    temperature: config.temperature,
    maxOutputTokens: config.maxOutputTokens,
  });

  const resp = await fetch(
    `https://nextchat.blendiv.com/api/google/v1beta/models/${config.modelName}:generateContent`,
    {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + config.apiKey.trim(),
      },
      body: JSON.stringify(requestBody),
    }
  );
  const json = await resp.json();
  if (!resp.ok) {
    throw new Error(`${resp.status} ${resp.statusText}\n${JSON.stringify(json)}`);
  }
  return await json.candidates?.[0]?.content?.parts;
}

export async function streamGeminiResponse(text: string, onchange: (text: string) => {}) {
  const config = get(geminiConfig);

  const requestBody = createGeminiRequest([text], {
    prompt: "请继写小说, 最多续写 100 字.",
    temperature: config.temperature,
    maxOutputTokens: config.maxOutputTokens,
  });

  try {
    const res = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/${config.modelName}:streamGenerateContent?alt=sse`,
      {
        method: "POST",
        headers: {
          Accept: "text/event-stream",
          "Content-Type": "application/json, text/event-stream",
          Authorization: "Bearer " + config.apiKey.trim(),
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!res.body) {
      throw new Error("No response body received");
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let textBuffer = "";

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");

      for (const line of lines) {
        if (line.startsWith("data:")) {
          try {
            const data = line.replace("data:", "").trim()
            console.log(data);

            const json = JSON.parse(data);
            const parts = json.candidates?.[0]?.content?.parts ?? [];
            for (const part of parts) {
              if (part.text) {
                textBuffer = textBuffer + part.text;
                onchange(textBuffer)
              }
            }
          } catch (err) {
            console.error("JSON parse error:", err, line);
          }
        }
      }
    }
  } catch (error) {
    console.error("API request failed:", error);
  }
}
