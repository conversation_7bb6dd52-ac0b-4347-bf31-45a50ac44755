<script lang="ts">
    import { autoFocus } from "$lib/actions/autoFocus";
    import {
        keyboardShortcut,
        type KeyboardShortcut,
    } from "$lib/actions/window";
    import { createEventDispatcher } from "svelte";
    const dispatch = createEventDispatcher();

    export let suggestion: string = "";
    export let value: string = "";
    export let placeholder = "";
    export let disabled: boolean = false;
    export let readonly: boolean = false;
    export let autofocus: boolean = false;
    export let shortcuts: KeyboardShortcut[] = [];
    export let selectStart = 0;

    export let style = "";
    export let flex = "";

    $: {
        if (textspan) {
            if (suggestion === "") {
                textspan.innerText = `${value} `;
                selectStart = textarea.selectionStart;
                textspan.style.visibility = "hidden";
            } else {
                textspan.innerText = `${suggestion} `;
                textspan.style.color = "#777";
                textspan.style.visibility = "";
            }

            // console.log("textspan", textspan.style.visibility || "show");

            dispatch("selectstart", textarea.selectionStart);
        }
    }

    let textspan: HTMLElement;
    let textarea: HTMLTextAreaElement;
</script>

<div class={flex}>
    <span bind:this={textspan} class={$$props.class} {style}></span>
    <textarea
        bind:this={textarea}
        class={$$props.class}
        {style}
        bind:value
        on:input
        {disabled}
        {readonly}
        {placeholder}
        use:autoFocus={autofocus}
        use:keyboardShortcut={shortcuts}
    ></textarea>
</div>

<style>
    div {
        position: relative;
        z-index: 5;
    }
    span {
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        text-align: left;
        width: 100%;
        height: 100%;
        resize: none;
        overflow: hidden;
    }
    textarea {
        text-align: left;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        resize: none;
        overflow: hidden;
        z-index: 4;
    }
</style>
